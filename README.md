# 绝缘子串多模态检测系统

基于RGB+热红外图像的绝缘子故障分类和定位深度学习模型。

## 📋 项目概述

本项目开发了一个多模态深度学习系统，用于检测电力系统中绝缘子的各种故障类型。该系统结合RGB图像和热红外图像，通过先进的特征融合技术实现高精度的故障检测和定位。

### 🌟 主要特性

- **🔀 多模态融合**: 支持RGB和热红外图像的多种融合策略
- **🎯 高精度检测**: 基于改进的YOLOv8架构实现精确的目标检测
- **🏷️ 7类故障检测**: 支持检测7种不同类型的绝缘子故障
- **⚙️ 灵活配置**: 支持多种融合方法和训练参数配置
- **🔄 完整流程**: 提供从数据预处理到模型部署的完整解决方案
- **💻 跨平台支持**: 支持Windows/Linux/macOS，CPU/GPU模式

### 🏷️ 支持的故障类型

1. **110_two_hight_glass**: 玻璃绝缘子
2. **Glass_Dirty**: 玻璃片脏污
3. **Glass_Loss**: 玻璃片缺损
4. **Polyme_Dirty**: 聚合物片脏污
5. **broken disc**: 聚合物片缺损
6. **insulator**: 聚合物绝缘子
7. **pollution-flashover**: 绝缘子闪络

## 📊 数据集信息

- **训练集**: 2,150 张图像
- **验证集**: 391 张图像
- **测试集**: 287 张图像
- **总计**: 2,828 张RGB图像 + 对应热红外图像

## 🚀 快速开始

### 1. 环境配置

```bash
# 创建虚拟环境
conda create -n multimodal python=3.9
conda activate multimodal

# 检查CUDA环境（GPU用户）
python scripts/check_cuda.py

# 安装依赖（GPU版本推荐）
pip install -r requirements/requirements.txt

# 或安装CPU版本
pip install -r requirements/requirements_cpu.txt
```

### 2. 🎬 新手推荐：一键演示体验

如果您是**第一次使用**，强烈建议先运行演示脚本了解系统功能：

```bash
# 🌟 完整演示（数据加载→训练→评估→推理→模态比较）
python src/inference/demo.py --mode all --epochs 5

# ⚡ 快速体验（仅数据加载演示）
python src/inference/demo.py --mode data

# 🔧 使用已有模型进行推理演示
python src/inference/demo.py --mode inference --model_path runs/train_optimized/20250729_153157/weights/best.pt
```

### 3. 正式训练（推荐）

#### 🥇 最佳配置训练

```bash
python train.py --config configs/config_optimized.yaml
```

#### ⚡ 快速验证

```bash
python scripts/quick_train.py --epochs 20 --batch_size 6
```

#### 🧪 Windows专用

```bash
python src/training/train_windows_fix.py --epochs 20 --batch_size 6
```

### 4. 模型评估

```bash
python evaluate.py --model_path runs/train_clean_dataset/fixed_20250802_141124/weights/best.pt --data_dir data
```

### 5. 模型推理

```bash
# 单张图像推理
python inference.py --model_path runs/train_optimized/xxx/weights/best.pt --image_path data/test/images/xxx.jpg

# 批量推理
python inference.py --model_path runs/train_optimized/xxx/weights/best.pt --input_dir data/test/images --output_dir results
```

## 📁 项目结构

```
绝缘子串多模态检测/
├── 📂 configs/              # 配置文件
│   ├── config_cpu.yaml      # CPU模式配置
│   ├── config_optimized.yaml # 🥇 优化配置（推荐）
│   ├── config_template.yaml # 配置模板
│   └── data.yaml            # 数据集配置
├── 📂 data/                 # 数据集目录
│   ├── 📂 train/           # 训练数据
│   │   ├── 📂 images/      # RGB图像
│   │   │   └── 📂 thermal/ # 热红外图像
│   │   └── 📂 labels/      # YOLO格式标注
│   ├── 📂 valid/           # 验证数据
│   └── 📂 test/            # 测试数据
├── 📂 src/                  # 核心源代码
│   ├── 📂 dataset/         # 数据处理模块
│   │   └── multimodal_dataset.py # 多模态数据加载器
│   ├── 📂 models/          # 模型定义
│   │   ├── multimodal_fusion.py     # 特征融合网络
│   │   ├── multimodal_yolo.py       # 多模态YOLO模型
│   │   └── multimodal_yolo_fixed.py # 修复版模型
│   ├── 📂 training/        # 训练模块
│   │   ├── train_multimodal.py     # 主训练脚本
│   │   ├── train_windows_fix.py    # Windows专用训练
│   │   └── evaluate_multimodal.py  # 评估脚本
│   ├── 📂 inference/       # 推理模块
│   │   ├── inference_multimodal.py # 推理引擎
│   │   └── demo.py         # 演示脚本
│   └── 📂 utils/           # 工具函数
├── 📂 scripts/             # 实用脚本
│   ├── check_cuda.py       # CUDA环境检测
│   ├── quick_train.py      # 快速训练脚本
│   └── fixed_batch_thermal_script.jsx # Photoshop批处理脚本
├── 📂 requirements/        # 依赖文件
│   ├── requirements.txt    # GPU版本依赖
│   └── requirements_cpu.txt # CPU版本依赖
├── 📂 tests/               # 测试文件（可选）
│   ├── test_config_loading.py    # 配置加载测试
│   ├── test_fixed_model.py       # 模型修复测试
│   └── test_windows_fix.py       # Windows兼容性测试
├── 📂 weights/             # 预训练权重
│   └── yolov8n.pt         # YOLOv8预训练模型
├── 📂 runs/                # 训练输出（自动生成）
│   ├── 📂 train/          # 基础训练结果
│   ├── 📂 train_optimized/ # 优化配置训练结果 🥇
│   ├── 📂 train_windows/  # Windows专用训练结果
│   └── 📂 demo_train/     # 演示训练结果
├── 📂 docs/                # 文档
│   ├── README.md          # 详细文档
│   ├── training_guide.md  # 训练指南
│   └── WINDOWS_FIX.md     # Windows问题解决方案
├── train.py               # 🚀 训练入口脚本
├── inference.py           # 🔮 推理入口脚本
├── evaluate.py            # 📊 评估入口脚本
└── README.md              # 项目说明
```

## ⚙️ 配置文件说明

### 🥇 `configs/config_optimized.yaml` （推荐）

- **用途**: 基于数据集分析的最佳参数配置
- **特点**: batch_size=6, epochs=80, 性能最优
- **适用**: GPU用户，追求最佳效果

### ⚡ `configs/config_cpu.yaml`

- **用途**: CPU模式优化配置
- **特点**: batch_size=2, epochs=30, 内存友好
- **适用**: 无GPU用户

### 📝 `configs/config_template.yaml`

- **用途**: 配置模板和参数说明
- **特点**: 包含所有可调参数的详细说明
- **适用**: 自定义配置参考

## 🏋️ 训练配置推荐

### 🎯 推荐参数（基于数据集分析）

| 参数                    | 推荐值 | 说明                         |
| ----------------------- | ------ | ---------------------------- |
| **batch_size**    | 6      | 基于4GB内存和2150样本优化    |
| **epochs**        | 80     | 多模态任务充分训练，配合早停 |
| **img_size**      | 512    | 精度与性能平衡               |
| **learning_rate** | 0.001  | Adam优化器稳定选择           |
| **patience**      | 15     | 早停机制，防止过拟合         |

### 🚀 不同场景的训练命令

```bash
# 🥇 最佳性能（推荐）
python train.py --config configs/config_optimized.yaml

# ⚡ 快速验证（2-3小时）
python src/training/train_windows_fix.py --epochs 20 --batch_size 6

# 🛡️ 保守稳定（内存紧张时）
python src/training/train_windows_fix.py --epochs 50 --batch_size 4

# 💻 CPU模式
python train.py --config configs/config_cpu.yaml

# 🔄 从检查点恢复
python train.py --config configs/config_optimized.yaml --resume runs/train_optimized/xxx/weights/last.pt
```

## 📈 训练输出结构

每次训练会在 `runs/` 目录下创建时间戳目录：

```
runs/train_optimized/20250729_153740/
├── config.yaml              # 训练配置备份
├── training_history.json    # 训练历史记录
├── 📂 weights/             # 模型权重
│   ├── best.pt            # 🏆 最佳模型（用于推理）
│   ├── last.pt            # 📍 最新模型（用于续训）
│   └── epoch_N.pt         # 🔄 定期检查点
└── 📂 logs/               # TensorBoard日志
    └── events.out.tfevents...
```

## 💾 权重文件说明

### 📍 预训练权重

- **`weights/yolov8n.pt`**: YOLOv8预训练模型，项目必需，不可删除

### 🏆 训练权重

- **`best.pt`**: 验证集最佳模型，**推理时使用**
- **`last.pt`**: 最新训练状态，**断点续训使用**
- **`epoch_N.pt`**: 定期检查点，版本管理用

## 🔀 特征融合策略

### 1. 跨模态注意力融合（推荐）

```yaml
model:
  fusion_type: "cross_attention"
```

### 2. 空间注意力融合

```yaml
model:
  fusion_type: "spatial_attention"
```

### 3. 特征金字塔融合

```yaml
model:
  fusion_type: "pyramid_fusion"
```

## 🛠️ 常见问题解决

### Q1: Windows系统多进程错误？

**症状**: `Can't pickle local object` 或 `EOFError` 错误

**解决方案**:

```bash
# 🥇 推荐：使用Windows专用脚本
python src/training/train_windows_fix.py --epochs 20 --batch_size 6

# 🔧 测试修复效果
python tests/test_windows_fix.py
```

### Q2: "TypeError: 'bool' object is not callable" 错误？

**症状**: ultralytics与PyTorch的train()方法冲突

**解决方案**:

```bash
# 🔧 运行修复测试
python tests/test_fixed_model.py

# ✅ 如果测试通过，直接训练
python train.py --config configs/config_optimized.yaml
```

### Q3: CUDA不可用？

**诊断步骤**:

```bash
# 1. 运行诊断
python scripts/check_cuda.py

# 2. 验证PyTorch
python -c "import torch; print(torch.cuda.is_available())"

# 3. 重装PyTorch（如需要）
pip uninstall torch torchvision torchaudio -y
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### Q4: 显存不足？

**解决方案**:

```bash
# 减小批次大小
python train.py --config configs/config_optimized.yaml --batch_size 4

# 或使用CPU模式
python train.py --config configs/config_cpu.yaml
```

### Q5: 训练时间估算

| 配置                 | 预期时间  | 说明              |
| -------------------- | --------- | ----------------- |
| 🥇 最佳配置 (80轮)   | 6-8小时   | GPU模式，推荐配置 |
| ⚡ 快速验证 (20轮)   | 2-3小时   | 效果验证          |
| 🛡️ 保守训练 (50轮) | 4-6小时   | 稳定训练          |
| 💻 CPU模式 (30轮)    | 12-24小时 | 无GPU环境         |

## 🎬 演示脚本使用

### `src/inference/demo.py` - 综合演示脚本

这是一个**完整的系统演示脚本**，展示从数据加载到模型推理的全流程。

#### 🔥 一键完整演示（推荐新手）

```bash
# 运行完整演示流程（数据加载→训练→评估→推理→模态比较）
python src/inference/demo.py --mode all --epochs 2

# 指定更多训练轮数
python src/inference/demo.py --mode all --epochs 10
```

#### 🎯 分模块演示

```bash
# 1. 数据加载演示（无需预训练模型）
python src/inference/demo.py --mode data

# 2. 训练演示（自动训练2轮，生成demo模型）
python src/inference/demo.py --mode train --epochs 2

# 3. 评估演示（需要先运行训练或提供模型路径）
python src/inference/demo.py --mode eval --model_path runs/demo_train/weights/best.pt

# 4. 推理演示（需要先运行训练或提供模型路径）
python src/inference/demo.py --mode inference --model_path runs/demo_train/weights/best.pt

# 5. 模态比较演示（比较RGB、热红外、多模态融合效果）
python src/inference/demo.py --mode compare --model_path runs/demo_train/weights/best.pt
```

#### 📋 演示脚本详细说明

| 模式          | 功能         | 是否需要预训练模型    | 输出位置                 |
| ------------- | ------------ | --------------------- | ------------------------ |
| `data`      | 数据加载演示 | ❌ 不需要             | 控制台输出               |
| `train`     | 训练演示     | ❌ 不需要（从头训练） | `runs/demo_train/`     |
| `eval`      | 评估演示     | ✅ 需要               | `runs/demo_eval/`      |
| `inference` | 推理演示     | ✅ 需要               | `runs/demo_inference/` |
| `compare`   | 模态比较演示 | ✅ 需要               | 控制台输出               |
| `all`       | 完整流程演示 | ❌ 不需要（自动训练） | 多个目录                 |

#### 💡 使用建议

**⚠️ 重要提示**：请确保在**项目根目录**下运行演示脚本！

**🌟 第一次使用**（完整体验）：

```bash
# 确保当前目录为：绝缘子串多模态检测/
python src/inference/demo.py --mode all --epochs 5
```

**⚡ 快速验证**（只测试推理）：

```bash
# 使用已有的最佳模型
python src/inference/demo.py --mode inference --model_path runs/train_optimized/20250729_153157/weights/best.pt
```

**🔧 故障排除**（测试数据加载）：

```bash
python src/inference/demo.py --mode data
```

**🛠️ 如果遇到导入错误**：

```bash
# Windows用户
set PYTHONPATH=%cd%;%PYTHONPATH%

# Linux/macOS用户  
export PYTHONPATH=$PWD:$PYTHONPATH

# 然后重新运行
python src/inference/demo.py --mode all --epochs 2
```

#### 📊 演示脚本输出示例

```
🚀 多模态绝缘子检测系统演示
==================================================
使用设备: cuda
CUDA可用: 是
GPU型号: NVIDIA GeForce RTX 4090

=== 演示数据加载 ===
✓ RGB图像形状: torch.Size([4, 3, 640, 640])
✓ 热红外图像形状: torch.Size([4, 1, 640, 640])
✓ 目标数量: 12
✓ 文件名示例: 000000795_jpg.rf.7c4c77b9f1ce1de5f273db59032649e9.jpg
数据加载演示完成！

=== 演示模型训练 ===
训练配置:
  - 训练轮数: 2
  - 批次大小: 4
  - 融合类型: cross_attention
开始训练演示...
✓ 训练演示完成！
模型权重保存在: runs/demo_train/weights/

=== 演示模型推理 ===
测试图像: data/test/images/000000795v_jpg.rf.c935dd667f5e982adc3b488963464a51.jpg
热红外图像: data/test/images/thermal/000000795v_jpg.rf.c935dd667f5e982adc3b488963464a51_thermal.png
开始推理演示...
✓ 检测到 3 个目标
  1. insulator: 0.856
  2. Glass_Dirty: 0.743
  3. pollution-flashover: 0.621
✓ 推理演示完成！
结果保存在: runs/demo_inference/demo_result.jpg

==================================================
🎯 演示完成！成功 5/5 个模块
🎉 所有演示模块都运行成功！
```

## 🧪 测试和验证

```bash
# 配置文件加载测试
python tests/test_config_loading.py

# 模型修复验证
python tests/test_fixed_model.py

# Windows兼容性测试
python tests/test_windows_fix.py

# 简单配置测试
python tests/test_config_simple.py
```

## 📚 技术支持

1. **📖 查看文档**: `docs/` 目录下的详细文档
2. **🔧 运行测试**: `tests/` 目录下的诊断脚本
3. **💡 参考示例**: `src/inference/demo.py` 演示脚本

## 📄 许可证

本项目采用 MIT 许可证。详情请参见 LICENSE 文件。

---

**💡 提示**: 推荐使用 `configs/config_optimized.yaml` 配置进行训练，该配置基于数据集规模和硬件分析优化得出。
