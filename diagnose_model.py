#!/usr/bin/env python3
"""
模型诊断脚本 - 检查模型输出和预测结果
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
import cv2

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

from src.models.fixed_multimodal_yolo import FixedMultimodalYOLO

def load_model(model_path):
    """加载模型"""
    print(f"🔧 加载模型: {model_path}")
    
    # 创建模型
    model = FixedMultimodalYOLO(
        yolo_model_path='yolov8n.pt',
        nc=5,
        fusion_type='cross_attention'
    )
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')
    if 'model' in checkpoint:
        model.load_state_dict(checkpoint['model'])
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    return model

def preprocess_image(image_path, img_size=640):
    """预处理图像"""
    # 加载RGB图像
    rgb_img = cv2.imread(image_path)
    rgb_img = cv2.cvtColor(rgb_img, cv2.COLOR_BGR2RGB)
    
    # 加载热红外图像
    thermal_path = image_path.replace('.jpg', '_thermal.png').replace('images', 'images/thermal')
    if os.path.exists(thermal_path):
        thermal_img = cv2.imread(thermal_path, cv2.IMREAD_GRAYSCALE)
        thermal_img = cv2.cvtColor(thermal_img, cv2.COLOR_GRAY2RGB)
    else:
        print(f"⚠️ 热红外图像不存在: {thermal_path}")
        thermal_img = np.zeros_like(rgb_img)
    
    # 调整大小
    rgb_img = cv2.resize(rgb_img, (img_size, img_size))
    thermal_img = cv2.resize(thermal_img, (img_size, img_size))
    
    # 转换为tensor
    rgb_tensor = torch.from_numpy(rgb_img).permute(2, 0, 1).float() / 255.0
    thermal_tensor = torch.from_numpy(thermal_img).permute(2, 0, 1).float() / 255.0
    
    return rgb_tensor.unsqueeze(0), thermal_tensor.unsqueeze(0)

def analyze_model_output(model, rgb_tensor, thermal_tensor):
    """分析模型输出"""
    with torch.no_grad():
        outputs = model(rgb_tensor, thermal_tensor)
    
    print(f"📊 模型输出分析:")
    print(f"  输出类型: {type(outputs)}")
    
    if isinstance(outputs, (list, tuple)):
        print(f"  输出数量: {len(outputs)}")
        for i, output in enumerate(outputs):
            if isinstance(output, torch.Tensor):
                print(f"  输出{i} 形状: {output.shape}")
                print(f"  输出{i} 数值范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
                print(f"  输出{i} 均值: {output.mean().item():.4f}")
    elif isinstance(outputs, torch.Tensor):
        print(f"  输出形状: {outputs.shape}")
        print(f"  数值范围: [{outputs.min().item():.4f}, {outputs.max().item():.4f}]")
        print(f"  均值: {outputs.mean().item():.4f}")
    
    return outputs

def check_predictions(outputs, conf_thresh=0.01):
    """检查预测结果"""
    print(f"\n🔍 预测结果分析 (置信度阈值: {conf_thresh}):")
    
    if isinstance(outputs, (list, tuple)):
        # YOLOv8格式输出
        for i, output in enumerate(outputs):
            if isinstance(output, torch.Tensor) and len(output.shape) == 3:
                # 假设格式为 [batch, num_boxes, 5+num_classes]
                batch_size, num_boxes, features = output.shape
                print(f"  检测层{i}: {batch_size} batch, {num_boxes} boxes, {features} features")
                
                # 分析置信度
                if features >= 5:  # 至少有x,y,w,h,conf
                    conf_scores = output[0, :, 4]  # 置信度分数
                    max_conf = conf_scores.max().item()
                    min_conf = conf_scores.min().item()
                    mean_conf = conf_scores.mean().item()
                    
                    print(f"    置信度范围: [{min_conf:.6f}, {max_conf:.6f}]")
                    print(f"    平均置信度: {mean_conf:.6f}")
                    
                    # 统计超过阈值的预测
                    valid_preds = (conf_scores > conf_thresh).sum().item()
                    print(f"    超过阈值的预测数: {valid_preds}/{num_boxes}")
                    
                    if valid_preds > 0:
                        valid_indices = conf_scores > conf_thresh
                        valid_confs = conf_scores[valid_indices]
                        print(f"    有效预测置信度: {valid_confs.tolist()}")

def main():
    model_path = "runs/train_progressive/20250802_170243/stage3/weights/best.pt"
    test_image = "data/test/images/100_PNG_jpg.rf.3511803ad7b07648ab43e7534941560f.jpg"
    
    print("🔍 模型诊断开始...")
    print(f"📁 模型路径: {model_path}")
    print(f"🖼️ 测试图像: {test_image}")
    print("-" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图像不存在: {test_image}")
        return
    
    # 加载模型
    try:
        model = load_model(model_path)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 预处理图像
    try:
        rgb_tensor, thermal_tensor = preprocess_image(test_image)
        print("✅ 图像预处理成功")
        print(f"  RGB tensor 形状: {rgb_tensor.shape}")
        print(f"  Thermal tensor 形状: {thermal_tensor.shape}")
    except Exception as e:
        print(f"❌ 图像预处理失败: {e}")
        return
    
    # 分析模型输出
    try:
        outputs = analyze_model_output(model, rgb_tensor, thermal_tensor)
        print("✅ 模型推理成功")
    except Exception as e:
        print(f"❌ 模型推理失败: {e}")
        return
    
    # 检查预测结果
    check_predictions(outputs, conf_thresh=0.01)
    check_predictions(outputs, conf_thresh=0.001)
    check_predictions(outputs, conf_thresh=0.0001)
    
    print("\n🎯 诊断建议:")
    print("1. 如果所有置信度都很低，可能需要降低评估阈值")
    print("2. 如果模型输出异常，可能是权重加载问题")
    print("3. 如果预处理有问题，检查图像路径和格式")

if __name__ == '__main__':
    main()
