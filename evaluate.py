#!/usr/bin/env python3
"""
多模态绝缘子检测评估入口脚本
"""

import os
import sys
import argparse

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

# 导入评估模块
from src.training.evaluate_multimodal import MultimodalEvaluator, load_config

def main():
    parser = argparse.ArgumentParser(description='多模态绝缘子检测模型评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型权重路径')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--data_dir', type=str, default='data', help='数据集目录')
    parser.add_argument('--output_dir', type=str, default='runs/eval', help='输出目录')
    parser.add_argument('--conf_thresh', type=float, default=0.01, help='置信度阈值（降低以检测更多目标）')
    parser.add_argument('--iou_thresh', type=float, default=0.5, help='IoU阈值（优化后默认0.5）')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    
    args = parser.parse_args()
    
    print("[EVALUATE] 启动多模态绝缘子检测评估...")
    print("[INFO] 项目根目录:", current_dir)
    print(f"[INFO] 模型路径: {args.model_path}")
    print(f"[INFO] 置信度阈值: {args.conf_thresh}")
    print("-" * 50)
    
    # 加载配置
    if args.config:
        config = load_config(args.config)
    else:
        config = {
            'data_dir': args.data_dir,
            'batch_size': args.batch_size,
            'img_size': 640,
            'num_workers': 4,
            'model': {
                'fusion_type': 'cross_attention'
            }
        }
    
    # 命令行参数覆盖配置
    config['data_dir'] = args.data_dir
    config['batch_size'] = args.batch_size

    # 验证数据目录路径
    test_images_path = os.path.join(config['data_dir'], 'test', 'images')
    if not os.path.exists(test_images_path):
        print(f"❌ [ERROR] 测试数据目录不存在: {test_images_path}")
        print(f"📁 [INFO] 当前数据目录设置: {config['data_dir']}")
        print("💡 [HINT] 请确保数据目录结构正确，或使用 --data_dir 参数指定正确路径")
        print("   例如: python evaluate.py --model_path [模型路径] --data_dir data")
        sys.exit(1)

    print(f"✅ [INFO] 数据目录验证通过: {config['data_dir']}")
    print(f"📁 [INFO] 测试图像路径: {test_images_path}")

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 开始评估
    evaluator = MultimodalEvaluator(config, args.model_path)
    metrics = evaluator.evaluate(conf_thresh=args.conf_thresh, iou_thresh=args.iou_thresh)
    evaluator.save_results(args.output_dir)

if __name__ == '__main__':
    main() 