import os

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
import argparse
import yaml
import json
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from src.dataset.multimodal_dataset import create_dataloader
from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO
from src.training.train_multimodal import load_config


class MultimodalEvaluator:
    """多模态模型评估器"""
    
    def __init__(self, config, model_path):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 类别名称
        with open('configs/data.yaml', 'r', encoding='utf-8') as f:
            data_config = yaml.safe_load(f)
        self.class_names = data_config['names']
        self.nc = len(self.class_names)
        
        # 加载模型
        self.model = self._load_model(model_path)
        
        # 创建测试数据加载器
        self.test_loader = create_dataloader(
            data_dir=config['data_dir'],
            split='test',
            batch_size=config.get('batch_size', 16),
            img_size=config.get('img_size', 640),
            num_workers=config.get('num_workers', 4),
            shuffle=False
        )
        
        # 评估结果存储
        self.predictions = []
        self.ground_truths = []
        self.all_outputs = []
        
        # 评估指标
        self.metrics = {}
    
    def _load_model(self, model_path):
        """加载训练好的模型"""
        print(f"加载模型: {model_path}")
        
        # 创建模型
        model = SimpleMultimodalYOLO(
            nc=self.nc,
            fusion_type=self.config['model'].get('fusion_type', 'cross_attention')
        )
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(self.device)
        model.eval()
        
        print("模型加载完成")
        return model
    
    def _postprocess_outputs(self, outputs, conf_thresh=0.25, iou_thresh=0.45):
        """后处理模型输出，进行NMS等操作"""
        # 修复：正确处理多尺度输出和batch维度
        if not outputs or len(outputs) == 0:
            print("⚠️ [DEBUG] 模型输出为空")
            return []

        print(f"🔍 [DEBUG] 模型输出数量: {len(outputs)}")
        for i, output in enumerate(outputs):
            print(f"🔍 [DEBUG] 输出{i}形状: {output.shape}")
            if output.numel() > 0:
                print(f"🔍 [DEBUG] 输出{i}范围: [{output.min():.4f}, {output.max():.4f}]")

        # 获取batch大小
        batch_size = outputs[0].shape[0]
        batch_detections = [[] for _ in range(batch_size)]

        # 处理每个尺度的输出
        for scale_idx, output in enumerate(outputs):
            if not isinstance(output, torch.Tensor):
                continue

            # 输出格式：[batch, num_anchors*H*W, num_classes + 5]
            for b in range(batch_size):
                pred = output[b]  # [num_anchors*H*W, num_classes + 5]

                if pred.shape[0] == 0:  # 空预测
                    continue

                # 提取置信度和类别概率
                obj_conf = torch.sigmoid(pred[..., 4])  # 对象置信度
                cls_logits = pred[..., 5:]  # 类别logits（不应用sigmoid）
                cls_conf = torch.softmax(cls_logits, dim=-1)  # 使用softmax而不是sigmoid

                # 计算最终置信度
                max_cls_conf, cls_pred = torch.max(cls_conf, dim=-1)
                final_conf = obj_conf * max_cls_conf

                # 调试信息
                print(f"🔍 [DEBUG] Batch {b}: 预测数量: {pred.shape[0]}")
                print(f"🔍 [DEBUG] 对象置信度范围: [{obj_conf.min():.4f}, {obj_conf.max():.4f}]")
                print(f"🔍 [DEBUG] 类别置信度范围: [{cls_conf.min():.4f}, {cls_conf.max():.4f}]")
                print(f"🔍 [DEBUG] 最终置信度范围: [{final_conf.min():.4f}, {final_conf.max():.4f}]")
                print(f"🔍 [DEBUG] 置信度阈值: {conf_thresh}")

                # 置信度过滤
                valid_mask = final_conf > conf_thresh
                valid_count = valid_mask.sum().item()
                print(f"🔍 [DEBUG] 通过置信度过滤的预测数: {valid_count}/{pred.shape[0]}")

                if not valid_mask.any():
                    print("⚠️ [DEBUG] 没有预测通过置信度过滤")
                    continue

                # 提取有效的预测
                valid_pred = pred[valid_mask]
                valid_conf = final_conf[valid_mask]
                valid_cls = cls_pred[valid_mask]

                # 修复：改进边界框坐标转换，确保精度
                boxes = valid_pred[:, :4].clone()
                # 确保坐标在[0,1]范围内，避免精度问题
                boxes[:, 0] = torch.clamp(valid_pred[:, 0] - valid_pred[:, 2] / 2, 0.0, 1.0)  # x1
                boxes[:, 1] = torch.clamp(valid_pred[:, 1] - valid_pred[:, 3] / 2, 0.0, 1.0)  # y1
                boxes[:, 2] = torch.clamp(valid_pred[:, 0] + valid_pred[:, 2] / 2, 0.0, 1.0)  # x2
                boxes[:, 3] = torch.clamp(valid_pred[:, 1] + valid_pred[:, 3] / 2, 0.0, 1.0)  # y2

                # 修复：确保边界框有效性
                valid_boxes_mask = (boxes[:, 2] > boxes[:, 0]) & (boxes[:, 3] > boxes[:, 1])
                if not valid_boxes_mask.any():
                    continue

                boxes = boxes[valid_boxes_mask]
                valid_conf = valid_conf[valid_boxes_mask]
                valid_cls = valid_cls[valid_boxes_mask]

                # 组合结果
                detections = torch.cat([
                    boxes,
                    valid_conf.unsqueeze(1),
                    valid_cls.unsqueeze(1).float()
                ], dim=1)

                # 累积每个batch的检测结果（来自所有尺度）
                batch_detections[b].append(detections)
        
        # 对每个batch的所有尺度检测结果进行合并和NMS
        final_outputs = []
        for b in range(batch_size):
            if batch_detections[b]:
                # 合并同一batch的所有尺度检测结果
                all_detections = torch.cat(batch_detections[b], dim=0)
                # 进行NMS
                nms_detections = self._simple_nms(all_detections, iou_thresh)
                final_outputs.append(nms_detections)
            else:
                final_outputs.append(torch.empty((0, 6)))
        
        return final_outputs
    
    def _simple_nms(self, detections, iou_thresh):
        """改进的非极大值抑制"""
        if len(detections) == 0:
            return detections

        # 确保detections是2D张量 [N, 6]
        if detections.dim() == 1:
            detections = detections.unsqueeze(0)

        # 按置信度排序
        conf_sort_index = torch.argsort(detections[:, 4], descending=True)
        detections = detections[conf_sort_index]

        # 分类别进行NMS
        unique_classes = torch.unique(detections[:, 5])
        final_detections = []

        for cls in unique_classes:
            cls_mask = detections[:, 5] == cls
            cls_detections = detections[cls_mask]

            if len(cls_detections) == 0:
                continue

            keep_indices = []
            for i in range(len(cls_detections)):
                # 检查当前检测是否已被抑制
                suppressed = False
                for j in keep_indices:
                    iou = self._compute_single_iou(cls_detections[i, :4], cls_detections[j, :4])
                    if iou > iou_thresh:
                        suppressed = True
                        break

                if not suppressed:
                    keep_indices.append(i)

            if keep_indices:
                final_detections.append(cls_detections[keep_indices])

        if final_detections:
            return torch.cat(final_detections, dim=0)
        else:
            return torch.empty((0, 6), device=detections.device)
    
    def _compute_iou(self, box1, box2):
        """计算IoU"""
        # box格式: [x1, y1, x2, y2]
        inter_x1 = torch.max(box1[:, 0:1], box2[:, 0:1].T)
        inter_y1 = torch.max(box1[:, 1:2], box2[:, 1:2].T)
        inter_x2 = torch.min(box1[:, 2:3], box2[:, 2:3].T)
        inter_y2 = torch.min(box1[:, 3:4], box2[:, 3:4].T)
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)
        
        box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        
        union_area = box1_area.unsqueeze(1) + box2_area.unsqueeze(0) - inter_area
        
        iou = inter_area / (union_area + 1e-6)
        return iou
    
    def evaluate(self, conf_thresh=0.25, iou_thresh=0.45):
        """评估模型性能"""
        print("开始评估...")
        
        all_predictions = []
        all_ground_truths = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(self.test_loader, desc="评估中")):
                rgb_images = batch['rgb'].to(self.device)
                thermal_images = batch['thermal'].to(self.device)
                targets = batch['targets']
                filenames = batch['filenames']
                
                # 模型推理
                outputs = self.model(rgb_images, thermal_images)
                
                # 后处理
                processed_outputs = self._postprocess_outputs(outputs, conf_thresh, iou_thresh)
                
                # 处理真实标签
                batch_size = rgb_images.shape[0]
                for i in range(batch_size):
                    # 提取当前图像的预测
                    if i < len(processed_outputs):
                        pred = processed_outputs[i]
                    else:
                        pred = torch.empty((0, 6))
                    
                    # 提取当前图像的真实标签
                    img_targets = targets[targets[:, 0] == i]  # 筛选当前图像的标签
                    gt = img_targets[:, 1:]  # 移除batch索引
                    
                    all_predictions.append({
                        'filename': filenames[i],
                        'predictions': pred.cpu().numpy() if len(pred) > 0 else np.empty((0, 6)),
                        'image_shape': (rgb_images.shape[2], rgb_images.shape[3])
                    })
                    
                    all_ground_truths.append({
                        'filename': filenames[i],
                        'ground_truth': gt.cpu().numpy() if len(gt) > 0 else np.empty((0, 5)),
                        'image_shape': (rgb_images.shape[2], rgb_images.shape[3])
                    })
        
        # 计算评估指标
        self.metrics = self._compute_metrics(all_predictions, all_ground_truths, iou_thresh)
        
        return self.metrics
    
    def _compute_metrics(self, predictions, ground_truths, iou_thresh=0.5):
        """计算评估指标"""
        print("计算评估指标...")
        
        metrics = {}
        
        # 为每个类别计算指标
        ap_per_class = []
        precision_per_class = []
        recall_per_class = []
        
        for cls_id in range(self.nc):
            # 收集该类别的所有预测和真实标签
            cls_predictions = []
            cls_ground_truths = []
            
            for pred_data, gt_data in zip(predictions, ground_truths):
                pred = pred_data['predictions']
                gt = gt_data['ground_truth']
                
                # 筛选当前类别的预测
                if len(pred) > 0:
                    cls_pred_mask = pred[:, 5] == cls_id
                    cls_pred = pred[cls_pred_mask]
                    cls_predictions.extend(cls_pred)
                
                # 筛选当前类别的真实标签
                if len(gt) > 0:
                    cls_gt_mask = gt[:, 0] == cls_id
                    cls_gt = gt[cls_gt_mask]
                    cls_ground_truths.extend(cls_gt)
            
            # 计算该类别的AP
            if len(cls_predictions) > 0 and len(cls_ground_truths) > 0:
                cls_predictions = np.array(cls_predictions)
                cls_ground_truths = np.array(cls_ground_truths)
                
                ap, precision, recall = self._compute_ap(
                    cls_predictions, cls_ground_truths, iou_thresh
                )
                
                ap_per_class.append(ap)
                precision_per_class.append(precision)
                recall_per_class.append(recall)
            else:
                ap_per_class.append(0.0)
                precision_per_class.append(0.0)
                recall_per_class.append(0.0)
        
        # 计算mAP
        metrics['mAP'] = np.mean(ap_per_class)
        metrics['AP_per_class'] = {self.class_names[i]: ap_per_class[i] for i in range(self.nc)}
        metrics['precision_per_class'] = {self.class_names[i]: precision_per_class[i] for i in range(self.nc)}
        metrics['recall_per_class'] = {self.class_names[i]: recall_per_class[i] for i in range(self.nc)}
        
        # 总体精确率和召回率
        metrics['overall_precision'] = np.mean(precision_per_class)
        metrics['overall_recall'] = np.mean(recall_per_class)
        
        # F1分数
        if metrics['overall_precision'] + metrics['overall_recall'] > 0:
            metrics['F1'] = 2 * (metrics['overall_precision'] * metrics['overall_recall']) / \
                           (metrics['overall_precision'] + metrics['overall_recall'])
        else:
            metrics['F1'] = 0.0
        
        return metrics
    
    def _compute_ap(self, predictions, ground_truths, iou_thresh):
        """计算Average Precision"""
        if len(predictions) == 0 or len(ground_truths) == 0:
            return 0.0, 0.0, 0.0
        
        # 按置信度排序
        sort_indices = np.argsort(predictions[:, 4])[::-1]
        predictions = predictions[sort_indices]
        
        tp = np.zeros(len(predictions))
        fp = np.zeros(len(predictions))
        
        # 标记已匹配的真实标签
        matched_gt = np.zeros(len(ground_truths), dtype=bool)
        
        for i, pred in enumerate(predictions):
            pred_box = pred[:4]
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt in enumerate(ground_truths):
                if matched_gt[j]:
                    continue
                
                gt_box = self._yolo_to_xyxy(gt[1:5])  # 转换YOLO格式到xyxy
                iou = self._compute_single_iou(pred_box, gt_box)
                
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
            
            if best_iou >= iou_thresh and best_gt_idx != -1:
                tp[i] = 1
                matched_gt[best_gt_idx] = True
            else:
                fp[i] = 1
        
        # 计算累积精确率和召回率
        tp_cumsum = np.cumsum(tp)
        fp_cumsum = np.cumsum(fp)
        
        recall = tp_cumsum / len(ground_truths) if len(ground_truths) > 0 else np.zeros_like(tp_cumsum)
        precision = tp_cumsum / (tp_cumsum + fp_cumsum + 1e-6)
        
        # 计算AP
        ap = self._compute_ap_from_pr_curve(precision, recall)
        
        # 返回最终的精确率和召回率
        final_precision = precision[-1] if len(precision) > 0 else 0.0
        final_recall = recall[-1] if len(recall) > 0 else 0.0
        
        return ap, final_precision, final_recall
    
    def _yolo_to_xyxy(self, yolo_box):
        """将YOLO格式转换为xyxy格式"""
        x_center, y_center, width, height = yolo_box
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        return np.array([x1, y1, x2, y2])
    
    def _compute_single_iou(self, box1, box2):
        """计算单个IoU"""
        x1_inter = max(box1[0], box2[0])
        y1_inter = max(box1[1], box2[1])
        x2_inter = min(box1[2], box2[2])
        y2_inter = min(box1[3], box2[3])
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def _compute_ap_from_pr_curve(self, precision, recall):
        """从PR曲线计算AP"""
        # 添加边界点
        recall = np.concatenate([np.array([0]), recall, np.array([1])])
        precision = np.concatenate([np.array([1]), precision, np.array([0])])
        
        # 确保精确率单调递减
        for i in range(len(precision) - 2, -1, -1):
            precision[i] = max(precision[i], precision[i + 1])
        
        # 计算AP（面积）
        ap = 0
        for i in range(len(recall) - 1):
            ap += (recall[i + 1] - recall[i]) * precision[i + 1]
        
        return ap
    
    def save_results(self, output_dir):
        """保存评估结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存指标
        with open(os.path.join(output_dir, 'metrics.json'), 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False)
        
        # 打印结果
        print("\n=== 评估结果 ===")
        print(f"mAP: {self.metrics['mAP']:.4f}")
        print(f"总体精确率: {self.metrics['overall_precision']:.4f}")
        print(f"总体召回率: {self.metrics['overall_recall']:.4f}")
        print(f"F1分数: {self.metrics['F1']:.4f}")
        
        print("\n各类别AP:")
        for cls_name, ap in self.metrics['AP_per_class'].items():
            print(f"  {cls_name}: {ap:.4f}")
        
        # 绘制结果图表
        self._plot_results(output_dir)
    
    def _plot_results(self, output_dir):
        """绘制评估结果图表"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 绘制每类别AP条形图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # AP条形图
        classes = list(self.metrics['AP_per_class'].keys())
        ap_values = list(self.metrics['AP_per_class'].values())
        
        ax1.bar(range(len(classes)), ap_values)
        ax1.set_xlabel('类别')
        ax1.set_ylabel('Average Precision')
        ax1.set_title('各类别Average Precision')
        ax1.set_xticks(range(len(classes)))
        ax1.set_xticklabels(classes, rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, v in enumerate(ap_values):
            ax1.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        # 精确率和召回率比较
        precision_values = list(self.metrics['precision_per_class'].values())
        recall_values = list(self.metrics['recall_per_class'].values())
        
        x_pos = np.arange(len(classes))
        width = 0.35
        
        ax2.bar(x_pos - width/2, precision_values, width, label='精确率', alpha=0.8)
        ax2.bar(x_pos + width/2, recall_values, width, label='召回率', alpha=0.8)
        
        ax2.set_xlabel('类别')
        ax2.set_ylabel('值')
        ax2.set_title('各类别精确率和召回率')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(classes, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'metrics_by_class.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 绘制总体指标雷达图
        metrics_names = ['mAP', '精确率', '召回率', 'F1分数']
        metrics_values = [
            self.metrics['mAP'],
            self.metrics['overall_precision'],
            self.metrics['overall_recall'],
            self.metrics['F1']
        ]
        
        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        
        angles = np.linspace(0, 2*np.pi, len(metrics_names), endpoint=False)
        angles = np.concatenate((angles, [angles[0]]))  # 闭合
        metrics_values = metrics_values + [metrics_values[0]]  # 闭合
        
        ax.plot(angles, metrics_values, 'o-', linewidth=2, label='模型性能')
        ax.fill(angles, metrics_values, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics_names)
        ax.set_ylim(0, 1)
        ax.set_title('模型总体性能雷达图', size=16, y=1.08)
        ax.grid(True)
        
        plt.savefig(os.path.join(output_dir, 'overall_metrics_radar.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"评估结果已保存到: {output_dir}")


def main():
    parser = argparse.ArgumentParser(description='多模态绝缘子检测模型评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型权重路径')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--data_dir', type=str, default='.', help='数据集目录')
    parser.add_argument('--output_dir', type=str, default='runs/eval', help='输出目录')
    parser.add_argument('--conf_thresh', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou_thresh', type=float, default=0.5, help='IoU阈值')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    
    args = parser.parse_args()
    
    # 加载配置
    if args.config:
        config = load_config(args.config)
    else:
        config = {
            'data_dir': args.data_dir,
            'batch_size': args.batch_size,
            'img_size': 640,
            'num_workers': 4,
            'model': {
                'fusion_type': 'cross_attention'
            }
        }
    
    # 命令行参数覆盖配置
    config['data_dir'] = args.data_dir
    config['batch_size'] = args.batch_size
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("评估配置:")
    print(yaml.dump(config, default_flow_style=False, allow_unicode=True))
    
    # 开始评估
    evaluator = MultimodalEvaluator(config, args.model_path)
    metrics = evaluator.evaluate(conf_thresh=args.conf_thresh, iou_thresh=args.iou_thresh)
    evaluator.save_results(args.output_dir)


if __name__ == '__main__':
    main() 