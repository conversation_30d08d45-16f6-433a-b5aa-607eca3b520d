#!/usr/bin/env python3
"""
多模态绝缘子检测训练入口脚本
修复版本 - 使用正确的类别数（nc=5）
"""

import os
import sys
import yaml
import argparse
from datetime import datetime

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

# 导入训练模块
from src.training.train_multimodal import MultimodalTrainer

def create_stage_config(stage, base_output_dir=None, previous_weights=None):
    """创建指定阶段的训练配置"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 阶段配置
    stage_configs = {
        1: {
            'active_classes': [0, 1, 3],  # 主要类别（87.9%数据）
            'epochs': 25,
            'lr': 0.00002,
            'description': '主要类别训练'
        },
        2: {
            'active_classes': [0, 1, 2, 3],  # 添加类别2
            'epochs': 20,
            'lr': 0.000015,  # 稍微降低学习率
            'description': '添加Glass_Loss类别'
        },
        3: {
            'active_classes': [0, 1, 2, 3, 4],  # 全部类别
            'epochs': 35,
            'lr': 0.00001,  # 精调阶段使用更低学习率
            'description': '全类别精调训练'
        }
    }

    stage_info = stage_configs[stage]
    output_dir = base_output_dir or f'runs/train_progressive/stage{stage}_{timestamp}'

    config = {
        'data_dir': 'data',
        'output_dir': output_dir,
        'nc': 5,  # 总类别数
        'img_size': 512,
        'batch_size': 4,  # 降低批次大小提高稳定性
        'epochs': stage_info['epochs'],
        'num_workers': 0,  # Windows兼容
        'grad_clip': 5.0,  # 降低梯度裁剪阈值

        # 渐进式训练配置
        'progressive_mode': True,
        'current_stage': stage,
        'active_classes': stage_info['active_classes'],
        'stage_description': stage_info['description'],
        'previous_weights': previous_weights,

        'model': {
            'type': 'simple',  # 使用simple类型，内部会创建FixedMultimodalYOLO
            'yolo_model_path': 'yolov8n.pt',
            'fusion_type': 'cross_attention'
        },

        'optimizer': {
            'type': 'Adam',
            'lr': stage_info['lr'],
            'weight_decay': 0.001  # 增加正则化强度防止过拟合
        },

        'scheduler': {
            'type': 'CosineAnnealingLR',
            'T_max': stage_info['epochs'],  # 与当前阶段epochs保持一致
            'eta_min': 1e-6
        },

        'early_stopping': {
            'patience': 12  # 增加耐心值
        },

        # 使用Focal Loss处理类别不平衡
        'use_focal_loss': True,

        # 损失权重配置 - 针对小目标和类别不平衡优化
        'loss_weights': {
            'box_loss': 2.0,  # 提高边界框损失权重，改善小目标检测
            'obj_loss': 1.5,  # 适中的置信度损失权重
            'cls_loss': 3.0   # 大幅提高类别损失权重，改善类别不平衡
        },

        # 针对大模型的额外配置
        'model_optimization': {
            'dropout_rate': 0.3,  # 增加Dropout防止过拟合
            'label_smoothing': 0.1,  # 标签平滑
            'gradient_accumulation': 2  # 梯度累积减少内存压力
        }
    }
    return config


def run_progressive_training():
    """执行完整的渐进式训练流程"""
    print("🚀 [PROGRESSIVE TRAINING] 开始渐进式训练流程")
    print("=" * 60)

    base_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_output_dir = f'runs/train_progressive/{base_timestamp}'

    # 存储每个阶段的最佳权重路径
    stage_weights = {}

    # 阶段1: 主要类别训练
    print("\n🎯 [STAGE 1] 主要类别训练 (类别: 0,1,3)")
    print("-" * 40)
    stage1_config = create_stage_config(1, f"{base_output_dir}/stage1")

    try:
        trainer = MultimodalTrainer(stage1_config)
        trainer.train()
        stage_weights[1] = f"{stage1_config['output_dir']}/weights/best.pt"
        print(f"✅ [STAGE 1] 完成! 权重保存至: {stage_weights[1]}")
    except Exception as e:
        print(f"❌ [STAGE 1] 失败: {e}")
        return False

    # 阶段2: 添加Glass_Loss类别
    print("\n🎯 [STAGE 2] 添加Glass_Loss类别 (类别: 0,1,2,3)")
    print("-" * 40)
    stage2_config = create_stage_config(2, f"{base_output_dir}/stage2", stage_weights[1])

    try:
        trainer = MultimodalTrainer(stage2_config)
        trainer.train()
        stage_weights[2] = f"{stage2_config['output_dir']}/weights/best.pt"
        print(f"✅ [STAGE 2] 完成! 权重保存至: {stage_weights[2]}")
    except Exception as e:
        print(f"❌ [STAGE 2] 失败: {e}")
        return False

    # 阶段3: 全类别精调
    print("\n🎯 [STAGE 3] 全类别精调训练 (类别: 0,1,2,3,4)")
    print("-" * 40)
    stage3_config = create_stage_config(3, f"{base_output_dir}/stage3", stage_weights[2])

    try:
        trainer = MultimodalTrainer(stage3_config)
        trainer.train()
        stage_weights[3] = f"{stage3_config['output_dir']}/weights/best.pt"
        print(f"✅ [STAGE 3] 完成! 权重保存至: {stage_weights[3]}")
    except Exception as e:
        print(f"❌ [STAGE 3] 失败: {e}")
        return False

    # 训练完成总结
    print("\n🎉 [COMPLETE] 渐进式训练全部完成!")
    print("=" * 60)
    print(f"📁 [OUTPUT] 总输出目录: {base_output_dir}")
    print(f"💾 [FINAL MODEL] 最终模型: {stage_weights[3]}")
    print("\n📊 [STAGE SUMMARY]:")
    for stage, weights_path in stage_weights.items():
        print(f"  阶段{stage}: {weights_path}")

    print(f"\n🔍 [EVALUATION] 评估最终模型:")
    print(f"  python evaluate.py --model_path {stage_weights[3]}")

    return True

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='多模态绝缘子检测训练（修复版）')
    parser.add_argument('--config', type=str, help='配置文件路径（可选）')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch_size', type=int, help='批次大小')
    parser.add_argument('--lr', type=float, help='学习率')
    parser.add_argument('--progressive', action='store_true', help='执行完整的渐进式训练（推荐）')
    parser.add_argument('--stage', type=int, choices=[1, 2, 3], help='执行单个训练阶段')
    parser.add_argument('--previous_weights', type=str, help='前一阶段的权重文件路径')

    args = parser.parse_args()

    print("🚀 [TRAIN] 启动渐进式多模态绝缘子检测训练...")
    print(f"📁 [INFO] 项目根目录: {current_dir}")

    # 检查是否执行完整的渐进式训练
    if args.progressive:
        print("\n🎯 [MODE] 完整渐进式训练模式")
        print("  将自动执行所有3个阶段的训练")
        print("\n📊 [STRATEGY] 渐进式训练策略:")
        print("  阶段1 (25轮): 训练主要类别 [0,1,3] (87.9%数据)")
        print("  阶段2 (20轮): 添加类别2 [0,1,2,3] (99.1%数据)")
        print("  阶段3 (35轮): 全类别训练 [0,1,2,3,4] (100%数据)")

        success = run_progressive_training()
        if success:
            print("\n🎉 [SUCCESS] 渐进式训练全部完成!")
        else:
            print("\n❌ [FAILED] 渐进式训练失败!")
        sys.exit(0)

    # 单阶段训练模式
    if args.stage:
        print(f"\n🎯 [MODE] 单阶段训练模式 - 阶段{args.stage}")
        training_config = create_stage_config(args.stage, previous_weights=args.previous_weights)

        # 命令行参数覆盖
        if args.epochs:
            training_config['epochs'] = args.epochs
        if args.batch_size:
            training_config['batch_size'] = args.batch_size
        if args.lr:
            training_config['optimizer']['lr'] = args.lr
    else:
        # 传统配置文件模式（向后兼容）
        print("\n🎯 [MODE] 传统配置文件模式")
        print("  建议使用 --progressive 参数执行完整渐进式训练")

        if args.config and os.path.exists(args.config):
            print(f"📄 [CONFIG] 加载配置文件: {args.config}")
            with open(args.config, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            training_config = config
        else:
            print("📄 [CONFIG] 使用默认阶段1配置")
            training_config = create_stage_config(1)

    # 命令行参数覆盖（仅在非渐进式模式下）
    if not args.progressive and not args.stage:
        if args.epochs:
            training_config['epochs'] = args.epochs
            print(f"🔧 [OVERRIDE] 训练轮数: {args.epochs}")
        if args.batch_size:
            training_config['batch_size'] = args.batch_size
            print(f"🔧 [OVERRIDE] 批次大小: {args.batch_size}")
        if args.lr:
            training_config['optimizer']['lr'] = args.lr
            print(f"🔧 [OVERRIDE] 学习率: {args.lr}")

    # 确保类别数正确
    if training_config['nc'] != 5:
        print(f"⚠️ [WARNING] 检测到nc={training_config['nc']}，强制修正为nc=5")
        training_config['nc'] = 5

    # Windows系统下强制设置num_workers=0
    import platform
    if platform.system() == 'Windows':
        print("🔧 [INFO] Windows系统检测到，自动设置 num_workers=0")
        training_config['num_workers'] = 0

    # 创建输出目录（如果还没有设置时间戳）
    if 'stage' not in training_config['output_dir']:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        training_config['output_dir'] = os.path.join(training_config['output_dir'], f'fixed_{timestamp}')

    os.makedirs(training_config['output_dir'], exist_ok=True)

    # 保存配置到输出目录
    config_save_path = os.path.join(training_config['output_dir'], 'config.yaml')
    with open(config_save_path, 'w', encoding='utf-8') as f:
        yaml.dump(training_config, f, default_flow_style=False, allow_unicode=True)

    print("\n📋 [CONFIG] 训练配置:")
    print(f"  数据目录: {training_config['data_dir']}")
    print(f"  输出目录: {training_config['output_dir']}")
    print(f"  类别数: {training_config['nc']}")
    print(f"  训练轮数: {training_config['epochs']}")
    print(f"  批次大小: {training_config['batch_size']}")
    print(f"  学习率: {training_config['optimizer']['lr']}")
    print(f"  图像尺寸: {training_config['img_size']}")

    print("\n🔧 [PROGRESSIVE] 渐进式训练配置:")
    print(f"  当前阶段: {training_config.get('current_stage', 1)}")
    print(f"  活跃类别: {training_config.get('active_classes', list(range(5)))}")
    print(f"  阶段描述: {training_config.get('stage_description', '单阶段训练')}")
    print(f"  使用Focal Loss: {training_config.get('use_focal_loss', False)}")
    if training_config.get('previous_weights'):
        print(f"  加载权重: {training_config['previous_weights']}")

    # 开始训练
    try:
        stage = training_config.get('current_stage', 1)
        print(f"\n🚀 [START] 开始阶段{stage}训练...")
        trainer = MultimodalTrainer(training_config)
        trainer.train()

        print(f"🎉 [SUCCESS] 阶段{stage}训练完成！")
        print(f"\n📁 [OUTPUT] 输出目录: {training_config['output_dir']}")
        print(f"💾 [MODEL] 最佳模型: {training_config['output_dir']}/weights/best.pt")
        print(f"📊 [LOGS] TensorBoard日志: {training_config['output_dir']}/logs")

        print(f"\n🔍 [EVALUATION] 评估阶段{stage}结果:")
        print(f"  python evaluate.py --model_path {training_config['output_dir']}/weights/best.pt")

        # 根据当前阶段提供不同的下一步建议
        if stage == 1:
            print(f"\n🎯 [NEXT STEPS] 继续阶段2训练:")
            print(f"  python train.py --stage 2 --previous_weights {training_config['output_dir']}/weights/best.pt")
        elif stage == 2:
            print(f"\n🎯 [NEXT STEPS] 继续阶段3训练:")
            print(f"  python train.py --stage 3 --previous_weights {training_config['output_dir']}/weights/best.pt")
        else:
            print(f"\n🎉 [COMPLETE] 渐进式训练全部完成!")
            print("  可以开始最终评估和部署")

    except Exception as e:
        print(f"❌ [ERROR] 训练失败: {e}")
        import traceback
        traceback.print_exc()